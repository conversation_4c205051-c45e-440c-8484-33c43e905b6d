package duckdb

import (
	"fmt"
	"strings"
	"time"

	"github.com/timescale/tsbs/cmd/tsbs_generate_queries/uses/devops"
	"github.com/timescale/tsbs/pkg/query"
)

// Devops produces DuckDB-specific queries for all the devops query types.
type Devops struct {
	*BaseGenerator
	*devops.Core
}

// getHostWhereWithHostnames creates WHERE conditions for hostnames
func (d *Devops) getHostWhereWithHostnames(hostnames []string) string {
	hostnameClauses := []string{}
	for _, s := range hostnames {
		hostnameClauses = append(hostnameClauses, fmt.Sprintf("JSON_EXTRACT(tags, '$.hostname') = '%s'", s))
	}
	return fmt.Sprintf("(%s)", strings.Join(hostnameClauses, " OR "))
}

// getHostWhereString gets multiple random hostnames and creates a WHERE SQL string for these hostnames.
func (d *Devops) getHostWhereString(nHosts int) string {
	hostnames, err := d.GetRandomHosts(nHosts)
	if err != nil {
		panic(err.Error())
	}
	return d.getHostWhereWithHostnames(hostnames)
}

// getSelectClausesAggMetrics builds SELECT clauses for aggregating metrics
func (d *Devops) getSelectClausesAggMetrics(agg string, metrics []string) []string {
	selectClauses := make([]string, len(metrics))
	for i, m := range metrics {
		selectClauses[i] = fmt.Sprintf("%s(CAST(JSON_EXTRACT(fields, '$.%s') AS DOUBLE)) AS %s_%s", agg, m, agg, m)
	}
	return selectClauses
}

// GroupByTime selects the MAX for numMetrics metrics under 'cpu',
// per minute for nhosts hosts,
// e.g. in pseudo-SQL:
//
// SELECT minute, max(metric1), ..., max(metricN)
// FROM cpu
// WHERE (hostname = '$HOSTNAME_1' OR ... OR hostname = '$HOSTNAME_N')
// AND time >= '$HOUR_START' AND time < '$HOUR_END'
// GROUP BY minute ORDER BY minute ASC
func (d *Devops) GroupByTime(qi query.Query, nHosts, numMetrics int, timeRange time.Duration) {
	interval := d.Interval.MustRandWindow(timeRange)
	metrics, err := devops.GetCPUMetricsSlice(numMetrics)
	if err != nil {
		panic(err.Error())
	}
	selectClauses := d.getSelectClausesAggMetrics("MAX", metrics)
	whereHosts := d.getHostWhereString(nHosts)

	humanLabel := fmt.Sprintf("DuckDB %d cpu metric(s), random %4d hosts, random %s by 1m", numMetrics, nHosts, timeRange)
	humanDesc := fmt.Sprintf("%s: %s", humanLabel, interval.StartString())

	sql := fmt.Sprintf(`SELECT
		DATE_TRUNC('minute', timestamp) AS minute,
		%s
	FROM metrics
	WHERE measurement = 'cpu'
		AND %s
		AND timestamp >= '%s'
		AND timestamp < '%s'
	GROUP BY minute
	ORDER BY minute ASC`,
		strings.Join(selectClauses, ",\n\t\t"),
		whereHosts,
		interval.Start().Format(goTimeFmt),
		interval.End().Format(goTimeFmt))

	d.fillInQuery(qi, humanLabel, humanDesc, sql)
}

// GroupByOrderByLimit benchmarks a query that has a time WHERE clause, that groups by a truncated date, orders by that date, and takes a limit:
// SELECT date_trunc('minute', time) AS t, MAX(cpu) FROM cpu
// WHERE time < '$TIME'
// GROUP BY t ORDER BY t DESC
// LIMIT $LIMIT
func (d *Devops) GroupByOrderByLimit(qi query.Query) {
	interval := d.Interval.MustRandWindow(devops.DoubleGroupByDuration)
	where := fmt.Sprintf("timestamp < '%s'", interval.End().Format(goTimeFmt))

	humanLabel := "DuckDB max cpu over last 5 min-intervals (random end)"
	humanDesc := fmt.Sprintf("%s: %s", humanLabel, interval.EndString())

	sql := fmt.Sprintf(`SELECT
		DATE_TRUNC('minute', timestamp) AS minute,
		MAX(CAST(JSON_EXTRACT(fields, '$.usage_user') AS DOUBLE)) AS max_usage_user
	FROM metrics
	WHERE measurement = 'cpu' AND %s
	GROUP BY minute
	ORDER BY minute DESC
	LIMIT 5`, where)

	d.fillInQuery(qi, humanLabel, humanDesc, sql)
}

// GroupByTimeAndPrimaryTag selects the AVG of numMetrics metrics under 'cpu' per device per hour for a day,
// e.g. in pseudo-SQL:
//
// SELECT AVG(metric1), ..., AVG(metricN) FROM cpu
// WHERE time >= '$HOUR_START' AND time < '$HOUR_END'
// AND (hostname = '$HOSTNAME_1' OR ... OR hostname = '$HOSTNAME_N')
// GROUP BY hour, hostname ORDER BY hour, hostname
func (d *Devops) GroupByTimeAndPrimaryTag(qi query.Query, numMetrics int) {
	metrics, err := devops.GetCPUMetricsSlice(numMetrics)
	if err != nil {
		panic(err.Error())
	}
	interval := d.Interval.MustRandWindow(devops.DoubleGroupByDuration)
	selectClauses := d.getSelectClausesAggMetrics("AVG", metrics)

	humanLabel := devops.GetDoubleGroupByLabel("DuckDB", numMetrics)
	humanDesc := fmt.Sprintf("%s: %s", humanLabel, interval.StartString())

	sql := fmt.Sprintf(`SELECT
		DATE_TRUNC('hour', timestamp) AS hour,
		JSON_EXTRACT(tags, '$.hostname') AS hostname,
		%s
	FROM metrics
	WHERE measurement = 'cpu'
		AND timestamp >= '%s'
		AND timestamp < '%s'
	GROUP BY hour, hostname
	ORDER BY hour, hostname`,
		strings.Join(selectClauses, ",\n\t\t"),
		interval.Start().Format(goTimeFmt),
		interval.End().Format(goTimeFmt))

	d.fillInQuery(qi, humanLabel, humanDesc, sql)
}

// MaxAllCPU selects the MAX of all metrics under 'cpu' per hour for nhosts hosts,
// e.g. in pseudo-SQL:
//
// SELECT MAX(metric1), ..., MAX(metricN)
// FROM cpu WHERE (hostname = '$HOSTNAME_1' OR ... OR hostname = '$HOSTNAME_N')
// AND time >= '$HOUR_START' AND time < '$HOUR_END'
// GROUP BY hour ORDER BY hour
func (d *Devops) MaxAllCPU(qi query.Query, nHosts int, duration time.Duration) {
	interval := d.Interval.MustRandWindow(duration)
	whereHosts := d.getHostWhereString(nHosts)
	selectClauses := d.getSelectClausesAggMetrics("MAX", devops.GetAllCPUMetrics())

	humanLabel := fmt.Sprintf("DuckDB max of all cpu metrics, random %4d hosts, random %s by 1h", nHosts, duration)
	humanDesc := fmt.Sprintf("%s: %s", humanLabel, interval.StartString())

	sql := fmt.Sprintf(`SELECT
		DATE_TRUNC('hour', timestamp) AS hour,
		%s
	FROM metrics
	WHERE measurement = 'cpu'
		AND %s
		AND timestamp >= '%s'
		AND timestamp < '%s'
	GROUP BY hour
	ORDER BY hour`,
		strings.Join(selectClauses, ",\n\t\t"),
		whereHosts,
		interval.Start().Format(goTimeFmt),
		interval.End().Format(goTimeFmt))

	d.fillInQuery(qi, humanLabel, humanDesc, sql)
}

// LastPointPerHost finds the last row for every host in the dataset
func (d *Devops) LastPointPerHost(qi query.Query) {
	humanLabel := "DuckDB last row per host"
	humanDesc := humanLabel

	sql := `SELECT DISTINCT
		JSON_EXTRACT(tags, '$.hostname') AS hostname,
		LAST(timestamp ORDER BY timestamp) AS last_timestamp
	FROM metrics
	WHERE measurement = 'cpu'
	GROUP BY hostname
	ORDER BY hostname`

	d.fillInQuery(qi, humanLabel, humanDesc, sql)
}

// HighCPUForHosts populates a query that gets CPU metrics when the CPU has high usage between a time period for a number of hosts
func (d *Devops) HighCPUForHosts(qi query.Query, nHosts int) {
	interval := d.Interval.MustRandWindow(devops.HighCPUDuration)
	whereHosts := d.getHostWhereString(nHosts)

	humanLabel := fmt.Sprintf("DuckDB CPU over threshold, random %4d hosts", nHosts)
	humanDesc := fmt.Sprintf("%s: %s", humanLabel, interval.StartString())

	sql := fmt.Sprintf(`SELECT *
	FROM metrics
	WHERE measurement = 'cpu'
		AND %s
		AND timestamp >= '%s'
		AND timestamp < '%s'
		AND CAST(JSON_EXTRACT(fields, '$.usage_user') AS DOUBLE) > 90.0
	ORDER BY timestamp`,
		whereHosts,
		interval.Start().Format(goTimeFmt),
		interval.End().Format(goTimeFmt))

	d.fillInQuery(qi, humanLabel, humanDesc, sql)
}
