package duckdb

import (
	"database/sql"
	"fmt"
	"log"

	_ "github.com/marcboeker/go-duckdb"
	"github.com/timescale/tsbs/pkg/targets"
)

// dbCreator handles database and table creation for DuckDB
type dbCreator struct {
	opts    *LoadingOptions
	ds      targets.DataSource
	driver  string
	connStr string
}

func (d *dbCreator) Init() {
	// DuckDB doesn't require explicit database creation for file-based databases
	// The database file will be created automatically when we connect
}

func (d *dbCreator) DBExists(dbName string) bool {
	// For file-based DuckDB, we can check if the file exists
	// For simplicity, we'll assume it doesn't exist and let DuckDB handle it
	return false
}

func (d *dbCreator) RemoveOldDB(dbName string) error {
	// For file-based DuckDB, we could remove the file
	// For now, we'll just return nil
	return nil
}

func (d *dbCreator) CreateDB(dbName string) error {
	db, err := sql.Open(d.driver, d.connStr)
	if err != nil {
		return fmt.Errorf("failed to open DuckDB connection: %v", err)
	}
	defer db.Close()

	// Test the connection
	if err := db.Ping(); err != nil {
		return fmt.Errorf("failed to ping DuckDB: %v", err)
	}

	// Create the metrics table if it doesn't exist
	if d.opts.CreateMetricsTable {
		if err := d.createMetricsTable(db); err != nil {
			return fmt.Errorf("failed to create metrics table: %v", err)
		}
	}

	return nil
}

func (d *dbCreator) createMetricsTable(db *sql.DB) error {
	// Drop existing table if it exists
	dropSQL := `DROP TABLE IF EXISTS metrics`
	if _, err := db.Exec(dropSQL); err != nil {
		return fmt.Errorf("failed to drop existing metrics table: %v", err)
	}

	// Create the metrics table
	// This is a simplified schema - you might want to customize based on your needs
	createSQL := `
		CREATE TABLE metrics (
			timestamp TIMESTAMP,
			measurement VARCHAR,
			tags JSON,
			fields JSON
		)
	`

	if _, err := db.Exec(createSQL); err != nil {
		return fmt.Errorf("failed to create metrics table: %v", err)
	}

	// Create indexes for better query performance
	indexSQL := `CREATE INDEX idx_metrics_timestamp ON metrics(timestamp)`
	if _, err := db.Exec(indexSQL); err != nil {
		log.Printf("Warning: failed to create timestamp index: %v", err)
	}

	indexSQL = `CREATE INDEX idx_metrics_measurement ON metrics(measurement)`
	if _, err := db.Exec(indexSQL); err != nil {
		log.Printf("Warning: failed to create measurement index: %v", err)
	}

	return nil
}

func (d *dbCreator) PostCreateDB(dbName string) error {
	// Any post-creation setup can go here
	return nil
}

func (d *dbCreator) Close() {
	// Nothing to close for the creator
}
