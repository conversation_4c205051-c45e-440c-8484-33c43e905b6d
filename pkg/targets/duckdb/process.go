package duckdb

import (
	"database/sql"
	"log"
	"strings"
	"time"

	_ "github.com/marcboeker/go-duckdb"
	"github.com/timescale/tsbs/pkg/targets"
)

// processor handles the insertion of batches into DuckDB
type processor struct {
	db     *sql.DB
	opts   *LoadingOptions
	dbName string
}

func newProcessor(opts *LoadingOptions, _ /* driver */, dbName string) targets.Processor {
	return &processor{
		opts:   opts,
		dbName: dbName,
	}
}

func (p *processor) Init(workerNum int, doLoad, hashWorkers bool) {
	if doLoad {
		connStr := p.opts.GetConnectString(p.dbName)
		db, err := sql.Open("duckdb", connStr)
		if err != nil {
			log.Fatalf("Failed to open DuckDB connection: %v", err)
		}
		p.db = db
	}
}

func (p *processor) ProcessBatch(b targets.Batch, doLoad bool) (uint64, uint64) {
	batch := b.(*batch)
	if !doLoad {
		return 0, uint64(batch.Len())
	}

	start := time.Now()

	// Create a transaction for batch processing
	tx, err := p.db.Begin()
	if err != nil {
		log.Fatalf("Failed to begin transaction: %v", err)
	}
	defer tx.Rollback()

	// Prepare insert statement
	// This is a simplified version - you might want to customize based on your schema
	insertSQL := `INSERT INTO metrics (timestamp, measurement, tags, fields) VALUES (?, ?, ?, ?)`
	stmt, err := tx.Prepare(insertSQL)
	if err != nil {
		log.Fatalf("Failed to prepare statement: %v", err)
	}
	defer stmt.Close()

	// Insert each row
	rowsInserted := uint64(0)
	for _, row := range batch.rows {
		// Parse the CSV row and insert
		// This is a simplified implementation
		parts := strings.Split(row, ",")
		if len(parts) >= 2 {
			_, err := stmt.Exec(parts[0], parts[1], "{}", "{}")
			if err != nil {
				log.Printf("Failed to insert row: %v", err)
				continue
			}
			rowsInserted++
		}
	}

	err = tx.Commit()
	if err != nil {
		log.Fatalf("Failed to commit transaction: %v", err)
	}

	took := time.Since(start)

	if p.opts.LogBatches {
		log.Printf("Processed batch of %d rows in %v", rowsInserted, took)
	}

	return rowsInserted, rowsInserted
}

func (p *processor) Close() {
	if p.db != nil {
		p.db.Close()
	}
}
