package duckdb

import (
	"hash/fnv"

	"github.com/timescale/tsbs/internal/inputs"
	"github.com/timescale/tsbs/pkg/data"
	"github.com/timescale/tsbs/pkg/data/source"
	"github.com/timescale/tsbs/pkg/targets"
)

const duckdbDriver = "duckdb"

func NewBenchmark(dbName string, opts *LoadingOptions, dataSourceConfig *source.DataSourceConfig) (targets.Benchmark, error) {
	var ds targets.DataSource
	if dataSourceConfig.Type == source.FileDataSourceType {
		ds = newFileDataSource(dataSourceConfig.File.Location)
	} else {
		dataGenerator := &inputs.DataGenerator{}
		simulator, err := dataGenerator.CreateSimulator(dataSourceConfig.Simulator)
		if err != nil {
			return nil, err
		}
		ds = newSimulationDataSource(simulator)
	}

	return &benchmark{
		opts:   opts,
		ds:     ds,
		dbName: dbName,
	}, nil
}

type benchmark struct {
	opts   *LoadingOptions
	ds     targets.DataSource
	dbName string
}

func (b *benchmark) GetDataSource() targets.DataSource {
	return b.ds
}

func (b *benchmark) GetBatchFactory() targets.BatchFactory {
	return &factory{}
}

func (b *benchmark) GetPointIndexer(maxPartitions uint) targets.PointIndexer {
	if maxPartitions > 1 {
		return &hostnameIndexer{partitions: maxPartitions}
	}
	return &targets.ConstantIndexer{}
}

func (b *benchmark) GetProcessor() targets.Processor {
	return newProcessor(b.opts, getDriver(), b.dbName)
}

func (b *benchmark) GetDBCreator() targets.DBCreator {
	return &dbCreator{
		opts:    b.opts,
		ds:      b.ds,
		driver:  getDriver(),
		connStr: b.opts.GetConnectString(b.dbName),
	}
}

func getDriver() string {
	return duckdbDriver
}

// factory creates batches for DuckDB
type factory struct{}

func (f *factory) New() targets.Batch {
	return &batch{
		rows: make([]string, 0, 10000),
	}
}

// batch holds a collection of rows for DuckDB
type batch struct {
	rows []string
}

func (b *batch) Len() uint {
	return uint(len(b.rows))
}

func (b *batch) Append(item data.LoadedPoint) {
	// Convert point to CSV row format
	// This is a simplified implementation - in practice you might want
	// to use the serializer or a more efficient format
	point := item.Data.(*data.Point)
	row := point.Timestamp().UTC().Format("2006-01-02 15:04:05.999999")
	row += "," + string(point.MeasurementName())
	// Add more fields as needed
	b.rows = append(b.rows, row)
}

// hostnameIndexer indexes points by hostname for partitioning
type hostnameIndexer struct {
	partitions uint
}

func (i *hostnameIndexer) GetIndex(item data.LoadedPoint) uint {
	point := item.Data.(*data.Point)
	tagValues := point.TagValues()

	// Look for hostname tag
	hostname := ""
	tagKeys := point.TagKeys()
	for j, key := range tagKeys {
		if string(key) == "hostname" {
			if j < len(tagValues) {
				if h, ok := tagValues[j].(string); ok {
					hostname = h
					break
				}
			}
		}
	}

	// Hash hostname to get partition
	if hostname == "" {
		return 0
	}

	h := fnv.New32a()
	h.Write([]byte(hostname))
	return uint(h.Sum32()) % i.partitions
}
